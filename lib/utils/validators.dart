/// Centralized validation utility class for form validation
/// Contains reusable validator functions for common input types
class Validators {
  // Private constructor to prevent instantiation
  Validators._();

  /// Email validation regex pattern
  static final RegExp _emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');

  /// Username validation regex pattern (alphanumeric, underscore, hyphen)
  static final RegExp _usernameRegex = RegExp(r'^[a-zA-Z0-9_-]+$');

  // ============================================================================
  // EMAIL VALIDATORS
  // ============================================================================

  /// Validates email format
  /// Returns error message if invalid, null if valid
  static String? validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required';
    }
    
    final email = value.trim();
    if (!_emailRegex.hasMatch(email)) {
      return 'Please enter a valid email address';
    }
    
    return null;
  }

  /// Validates email or username (allows both formats)
  /// Returns error message if invalid, null if valid
  static String? validateEmailOrUsername(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email or username is required';
    }
    
    final input = value.trim();
    
    // If it contains @, validate as email
    if (input.contains('@')) {
      if (!_emailRegex.hasMatch(input)) {
        return 'Please enter a valid email address';
      }
    } else {
      // Validate as username
      if (input.length < 3) {
        return 'Username must be at least 3 characters';
      }
      if (input.length > 30) {
        return 'Username must be less than 30 characters';
      }
      if (!_usernameRegex.hasMatch(input)) {
        return 'Username can only contain letters, numbers, underscore, and hyphen';
      }
    }
    
    return null;
  }

  // ============================================================================
  // PASSWORD VALIDATORS
  // ============================================================================

  /// Validates password with basic requirements
  /// Returns error message if invalid, null if valid
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    
    if (value.length < 8) {
      return 'Password must be at least 8 characters';
    }
    
    if (value.length > 128) {
      return 'Password must be less than 128 characters';
    }
    
    return null;
  }

  /// Validates password with strong requirements
  /// Returns error message if invalid, null if valid
  static String? validateStrongPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }

    if (value.length < 8) {
      return 'Password must be at least 8 characters';
    }

    if (value.length > 128) {
      return 'Password must be less than 128 characters';
    }

    if (!value.contains(RegExp(r'[A-Z]'))) {
      return 'Password must contain at least one uppercase letter';
    }

    if (!value.contains(RegExp(r'[a-z]'))) {
      return 'Password must contain at least one lowercase letter';
    }

    if (!value.contains(RegExp(r'[0-9]'))) {
      return 'Password must contain at least one number';
    }

    return null;
  }

  /// Validates password confirmation
  /// Returns error message if passwords don't match, null if valid
  static String? validatePasswordConfirmation(String? value, String? originalPassword) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }
    
    if (value != originalPassword) {
      return 'Passwords do not match';
    }
    
    return null;
  }

  // ============================================================================
  // NAME VALIDATORS
  // ============================================================================

  /// Validates full name
  /// Returns error message if invalid, null if valid
  static String? validateFullName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Full name is required';
    }
    
    final name = value.trim();
    if (name.length < 2) {
      return 'Full name must be at least 2 characters';
    }
    
    if (name.length > 50) {
      return 'Full name must be less than 50 characters';
    }
    
    // Allow letters, spaces, hyphens, and apostrophes
    if (!RegExp(r"^[a-zA-Z\s\-']+$").hasMatch(name)) {
      return 'Full name can only contain letters, spaces, hyphens, and apostrophes';
    }
    
    return null;
  }

  /// Validates username
  /// Returns error message if invalid, null if valid
  static String? validateUsername(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Username is required';
    }
    
    final username = value.trim();
    if (username.length < 3) {
      return 'Username must be at least 3 characters';
    }
    
    if (username.length > 30) {
      return 'Username must be less than 30 characters';
    }
    
    if (!_usernameRegex.hasMatch(username)) {
      return 'Username can only contain letters, numbers, underscore, and hyphen';
    }
    
    // Username cannot start or end with underscore or hyphen
    if (username.startsWith('_') || username.startsWith('-') ||
        username.endsWith('_') || username.endsWith('-')) {
      return 'Username cannot start or end with underscore or hyphen';
    }
    
    return null;
  }

  // ============================================================================
  // PHONE VALIDATORS
  // ============================================================================

  /// Validates phone number
  /// Returns error message if invalid, null if valid
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Phone number is required';
    }
    
    final phone = value.trim().replaceAll(RegExp(r'[\s\-\(\)]'), '');
    
    if (phone.length < 10) {
      return 'Phone number must be at least 10 digits';
    }
    
    if (phone.length > 15) {
      return 'Phone number must be less than 15 digits';
    }
    
    if (!RegExp(r'^[\+]?[0-9]+$').hasMatch(phone)) {
      return 'Please enter a valid phone number';
    }
    
    return null;
  }

  // ============================================================================
  // GENERIC VALIDATORS
  // ============================================================================

  /// Validates required field
  /// Returns error message if empty, null if valid
  static String? validateRequired(String? value, [String? fieldName]) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'This field'} is required';
    }
    return null;
  }

  /// Validates minimum length
  /// Returns error message if too short, null if valid
  static String? validateMinLength(String? value, int minLength, [String? fieldName]) {
    if (value == null || value.length < minLength) {
      return '${fieldName ?? 'This field'} must be at least $minLength characters';
    }
    return null;
  }

  /// Validates maximum length
  /// Returns error message if too long, null if valid
  static String? validateMaxLength(String? value, int maxLength, [String? fieldName]) {
    if (value != null && value.length > maxLength) {
      return '${fieldName ?? 'This field'} must be less than $maxLength characters';
    }
    return null;
  }

  /// Validates numeric input
  /// Returns error message if not numeric, null if valid
  static String? validateNumeric(String? value, [String? fieldName]) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'This field'} is required';
    }
    
    if (double.tryParse(value.trim()) == null) {
      return '${fieldName ?? 'This field'} must be a valid number';
    }
    
    return null;
  }

  /// Validates URL format
  /// Returns error message if invalid, null if valid
  static String? validateUrl(String? value, [String? fieldName]) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'URL'} is required';
    }
    
    final url = value.trim();
    if (!RegExp(r'^https?:\/\/.+').hasMatch(url)) {
      return 'Please enter a valid URL starting with http:// or https://';
    }
    
    return null;
  }
}
