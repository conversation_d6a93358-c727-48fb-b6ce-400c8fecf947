import 'package:darve/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/services/error/error_models.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ForgotPasswordController extends GetxController {
  RxBool isEmailSent = false.obs;
  String usernameOrEmail = '';
  late final AuthService authService;

  // Form validation state
  RxList<String> validationErrors = <String>[].obs;

  @override
  void onInit() {
    super.onInit();
    authService = AuthProvider.auth;
  }

  // Computed property to get loading state from auth service
  bool get isLoading => authService.loadingState == AuthLoadingState.loading;

  // Computed property to get error from auth service
  AuthError? get error => authService.error;

  /// Validates the forgot password form
  /// Returns true if validation passes, false otherwise
  bool validateForgotPasswordForm() {
    validationErrors.clear();
    bool isValid = true;

    // Email/Username validation
    if (usernameOrEmail.trim().isEmpty) {
      isValid = false;
      validationErrors.add("Email or username is required");
    } else if (_isEmail(usernameOrEmail.trim()) && !_isValidEmail(usernameOrEmail.trim())) {
      isValid = false;
      validationErrors.add("Please enter a valid email address");
    }

    return isValid;
  }

  /// Checks if input looks like an email (contains @)
  bool _isEmail(String input) {
    return input.contains('@');
  }

  /// Validates email format using regex
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Checks if a specific validation error exists
  bool hasValidationError(String errorMessage) {
    return validationErrors.contains(errorMessage);
  }

  /// Clears all validation errors
  void clearValidationErrors() {
    validationErrors.clear();
  }

  Future<void> forgotPassword() async {
    if (!validateForgotPasswordForm()) {
      return; // Don't proceed if validation fails
    }

    try {
      await authService.forgotPassword(usernameOrEmail.trim());

      // Check for errors
      if (authService.error != null) {
        // Error will be handled by the UI observing the auth service
        return;
      }

      // Success
      usernameOrEmail = '';
      isEmailSent.value = true;
    } catch (e) {
      debugPrint("Error sending reset link: $e");
    }
  }

  void clearError() {
    authService.clearError();
  }
}
