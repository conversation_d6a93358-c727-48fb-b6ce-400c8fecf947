import 'package:darve/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/services/error/error_models.dart';
import 'package:darve/utils/validators.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ForgotPasswordController extends GetxController {
  RxBool isEmailSent = false.obs;
  final usernameOrEmailController = TextEditingController();
  late final AuthService authService;

  // Form key for validation
  final formKey = GlobalKey<FormState>();

  @override
  void onInit() {
    super.onInit();
    authService = AuthProvider.auth;
  }

  // Computed property to get loading state from auth service
  bool get isLoading => authService.loadingState == AuthLoadingState.loading;

  // Computed property to get error from auth service
  AuthError? get error => authService.error;

  // Form validation method - delegate to centralized validators
  String? validateUsernameOrEmail(String? value) => Validators.validateEmailOrUsername(value);

  Future<void> forgotPassword() async {
    if (!formKey.currentState!.validate()) {
      return; // Don't proceed if validation fails
    }

    try {
      await authService.forgotPassword(usernameOrEmailController.text.trim());

      // Check for errors
      if (authService.error != null) {
        // Error will be handled by the UI observing the auth service
        return;
      }

      // Success
      usernameOrEmailController.clear();
      isEmailSent.value = true;
    } catch (e) {
      debugPrint("Error sending reset link: $e");
    }
  }

  void clearError() {
    authService.clearError();
  }

  @override
  void onClose() {
    usernameOrEmailController.dispose();
    super.onClose();
  }
}
