import 'package:darve/controllers/auth_controller.dart';
import 'package:darve/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/services/error/error_models.dart';
import 'package:get/get.dart';

class RegisterController extends GetxController {
  RxList<String> errorsArray = <String>[].obs;

  String enteredEmail = '';
  String enteredFullName = '';
  String username = '';
  String enteredPassword1 = '';
  String enteredPassword2 = '';

  String filepath = '';

  RxBool isPasswordVisible = false.obs;
  RxBool isFirstStage = true.obs;
  RxBool isCheckBoxClicked = false.obs;

  List<String> days = List.generate(31, (index) => (index + 1).toString());
  List<String> months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "June",
    "July",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec"
  ];
  List<String> years =
      List.generate(100, (index) => (DateTime.now().year - index).toString());

  RxString selectedDay = '1'.obs;
  RxString selectedMonth = 'Jan'.obs;
  RxString selectedYear = DateTime.now().year.toString().obs;

  AuthController? auth;
  late final AuthService authService;

  @override
  void onInit() {
    super.onInit();
    try {
      auth = Get.find<AuthController>();
    } catch (e) {
      // AuthController not available (e.g., in tests)
      auth = null;
    }
    authService = AuthProvider.auth;
  }

  // Computed property to get loading state from auth service
  bool get isLoading => authService.loadingState == AuthLoadingState.loading;

  // Computed property to get error from auth service
  AuthError? get error => authService.error;

  /// Validates the first stage form (email, name, passwords)
  /// Returns true if validation passes, false otherwise
  bool validateFirstStage() {
    errorsArray.clear(); // Clear previous errors
    bool isValidated = true;

    // Email validation
    if (enteredEmail.isEmpty) {
      isValidated = false;
      errorsArray.add("Email is required");
    } else if (!_isValidEmail(enteredEmail)) {
      isValidated = false;
      errorsArray.add("Please enter a valid email address");
    }

    // Full name validation
    if (enteredFullName.isEmpty || enteredFullName.length <= 3) {
      isValidated = false;
      errorsArray.add("Full Name should be at least 4 characters");
    }

    // Password validation
    if (enteredPassword1.isEmpty || enteredPassword1.length < 8) {
      isValidated = false;
      errorsArray.add("Password must be at least 8 characters");
    }

    // Re-entered password validation
    if (enteredPassword2.isEmpty || enteredPassword2.length < 8) {
      isValidated = false;
      errorsArray.add("Re-entered Password must be at least 8 characters");
    }

    // Password match validation
    if (enteredPassword1 != enteredPassword2) {
      isValidated = false;
      errorsArray.add("Passwords do not match");
    }

    return isValidated;
  }

  /// Proceeds to the next stage if validation passes
  void proceedToNextStage() {
    if (validateFirstStage()) {
      isFirstStage.value = false;
    }
  }

  /// Validates email format using regex
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Clears all form errors
  void clearErrors() {
    errorsArray.clear();
  }

  /// Checks if a specific error exists in the errors array
  bool hasError(String errorMessage) {
    return errorsArray.contains(errorMessage);
  }

  void register() async {
    try {
      // Use new auth service for registration
      await authService.register(
        username: username,
        password: enteredPassword1,
        email: enteredEmail,
        name: enteredFullName,
        imageUri: 'https://avatars.githubusercontent.com/u/185073648?s=200&v=4',
      );

      // Handle errors
      if (authService.error != null) {
        errorsArray.add(authService.error!.message);
        authService.clearError();
        return;
      }

      // If successful, navigate back
      if (authService.isLoggedIn) {
        Get.back();
      }
    } catch (error) {
      errorsArray.add(error.toString());
      rethrow;
    }
  }
}
