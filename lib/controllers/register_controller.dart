import 'package:darve/controllers/auth_controller.dart';
import 'package:darve/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/services/error/error_models.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class RegisterController extends GetxController {
  RxList<String> errorsArray = <String>[].obs;

  // Form controllers
  final emailController = TextEditingController();
  final fullNameController = TextEditingController();
  final usernameController = TextEditingController();
  final password1Controller = TextEditingController();
  final password2Controller = TextEditingController();

  String filepath = '';

  RxBool isPasswordVisible = false.obs;
  RxBool isFirstStage = true.obs;
  RxBool isCheckBoxClicked = false.obs;

  // Form keys for validation
  final firstStageFormKey = GlobalKey<FormState>();

  List<String> days = List.generate(31, (index) => (index + 1).toString());
  List<String> months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "June",
    "July",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec"
  ];
  List<String> years =
      List.generate(100, (index) => (DateTime.now().year - index).toString());

  RxString selectedDay = '1'.obs;
  RxString selectedMonth = 'Jan'.obs;
  RxString selectedYear = DateTime.now().year.toString().obs;

  AuthController? auth;
  late final AuthService authService;

  @override
  void onInit() {
    super.onInit();
    try {
      auth = Get.find<AuthController>();
    } catch (e) {
      // AuthController not available (e.g., in tests)
      auth = null;
    }
    authService = AuthProvider.auth;
  }

  // Computed property to get loading state from auth service
  bool get isLoading => authService.loadingState == AuthLoadingState.loading;

  // Computed property to get error from auth service
  AuthError? get error => authService.error;

  // Form validation methods
  String? validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value.trim())) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  String? validateFullName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Full name is required';
    }
    if (value.trim().length <= 3) {
      return 'Full name should be at least 4 characters';
    }
    return null;
  }

  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 8) {
      return 'Password must be at least 8 characters';
    }
    return null;
  }

  String? validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }
    if (value.length < 8) {
      return 'Password must be at least 8 characters';
    }
    if (value != password1Controller.text) {
      return 'Passwords do not match';
    }
    return null;
  }

  /// Proceeds to the next stage if validation passes
  void proceedToNextStage() {
    if (firstStageFormKey.currentState!.validate()) {
      isFirstStage.value = false;
    }
  }

  /// Clears all form errors
  void clearErrors() {
    errorsArray.clear();
  }

  void register() async {
    try {
      // Use new auth service for registration
      await authService.register(
        username: usernameController.text.trim(),
        password: password1Controller.text,
        email: emailController.text.trim(),
        name: fullNameController.text.trim(),
        imageUri: 'https://avatars.githubusercontent.com/u/185073648?s=200&v=4',
      );

      // Handle errors
      if (authService.error != null) {
        errorsArray.add(authService.error!.message);
        authService.clearError();
        return;
      }

      // If successful, navigate back
      if (authService.isLoggedIn) {
        Get.back();
      }
    } catch (error) {
      errorsArray.add(error.toString());
      rethrow;
    }
  }

  @override
  void onClose() {
    emailController.dispose();
    fullNameController.dispose();
    usernameController.dispose();
    password1Controller.dispose();
    password2Controller.dispose();
    super.onClose();
  }
}
